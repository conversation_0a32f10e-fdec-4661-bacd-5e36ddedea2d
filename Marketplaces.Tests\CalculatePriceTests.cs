using Marketplaces.Api.Controllers;
using Marketplaces.Api.Databases;
using Marketplaces.Api.Models;
using Marketplaces.Api.Requests;
using Marketplaces.Api.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Marketplaces.Tests;

public class CalculatePriceTests
{
    private DatabaseContext GetInMemoryContext()
    {
        var options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;
        return new DatabaseContext(options);
    }

    private PricesController CreateController(DatabaseContext context)
    {
        var controller = new PricesController(
            context,
            null!, // WildberriesClient
            null!, // OzonClient  
            null!, // YandexClient
            null!, // IMapper
            null!, // ILogger
            null!  // DiscountCalculationService
        );

        // Set up user context
        var user = new ClaimsPrincipal(new ClaimsIdentity(new[]
        {
            new Claim(ClaimTypes.NameIdentifier, "test-user-id")
        }));

        controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { User = user }
        };

        return controller;
    }

    [Fact]
    public async Task CalculatePrice_WithValidData_ShouldCalculateCorrectly()
    {
        // Arrange
        using var context = GetInMemoryContext();

        var shop = new Shop("Test Shop", "test-user-id")
        {
            BankTax = 7m,
            WildberriesTax = 35m
        };

        context.Shops.Add(shop);
        await context.SaveChangesAsync();

        var controller = CreateController(context);

        var request = new CalculatePriceRequest
        {
            PurchasePrice = 130m,
            BasePrice = 796m,
            DiscountPercent = 48m,
            Marketplace = Marketplace.Wildberries
        };

        // Act
        var result = await controller.CalculatePrice(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsType<CalculatePriceResponse>(okResult.Value);

        Assert.Equal(130m, response.PurchasePrice);
        Assert.Equal(796m, response.BasePrice);
        Assert.Equal(48m, response.DiscountPercent);
        Assert.Equal(413.92m, response.SalePrice);
        Assert.Equal(28.97m, response.BankCommissionAmount);
        Assert.Equal(144.87m, response.MarketplaceCommissionAmount);
        Assert.Equal(240.07m, response.Revenue);
        Assert.Equal(110.07m, response.Profit);
        Assert.Equal(84.67m, response.ProfitPercentage);
    }

    [Fact]
    public async Task CalculatePrice_WithoutDiscount_ShouldCalculateCorrectly()
    {
        // Arrange
        using var context = GetInMemoryContext();

        var shop = new Shop("Test Shop", "test-user-id")
        {
            BankTax = 7m,
            OzonTax = 35m
        };

        context.Shops.Add(shop);
        await context.SaveChangesAsync();

        var controller = CreateController(context);

        var request = new CalculatePriceRequest
        {
            PurchasePrice = 100m,
            BasePrice = 200m,
            DiscountPercent = 0m,
            Marketplace = Marketplace.Ozon
        };

        // Act
        var result = await controller.CalculatePrice(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsType<CalculatePriceResponse>(okResult.Value);

        Assert.Equal(100m, response.PurchasePrice);
        Assert.Equal(200m, response.BasePrice);
        Assert.Equal(0m, response.DiscountPercent);
        Assert.Equal(200m, response.SalePrice);
        Assert.Equal(14m, response.BankCommissionAmount);
        Assert.Equal(70m, response.MarketplaceCommissionAmount);
        Assert.Equal(116m, response.Revenue);
        Assert.Equal(16m, response.Profit);
        Assert.Equal(16m, response.ProfitPercentage);
    }
}

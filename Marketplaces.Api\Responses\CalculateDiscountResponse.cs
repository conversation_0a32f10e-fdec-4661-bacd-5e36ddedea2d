namespace Marketplaces.Api.Responses;

public class CalculateDiscountResponse
{
    public decimal CalculatedDiscountPercent { get; set; }
    public decimal DiscountForMinimumProfit { get; set; }
    public decimal DiscountForMinimumProfitPercentage { get; set; }
    public decimal UsedMinimumRevenue { get; set; }
    public decimal UsedMinimumProfitPercentage { get; set; }
    public string MinimumRevenueSource { get; set; } = string.Empty;
    public string MinimumProfitPercentageSource { get; set; } = string.Empty;
}

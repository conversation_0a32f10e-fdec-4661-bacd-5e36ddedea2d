namespace Marketplaces.Api.Services;

public class DiscountCalculationService
{
    /// <summary>
    /// Calculates the optimal discount percentage based on minimum profit and minimum profit percentage
    /// </summary>
    /// <param name="basePrice">Base price of the product</param>
    /// <param name="purchasePrice">Purchase price</param>
    /// <param name="minimumRevenue">Minimum profit amount</param>
    /// <param name="minimumProfitPercentage">Minimum profit percentage</param>
    /// <param name="bankTax">Bank commission in percent</param>
    /// <param name="marketplaceTax">Marketplace commission in percent</param>
    /// <returns>Calculated discount percentage</returns>
    public (decimal calculatedDiscount, decimal discountForMinProfit, decimal discountForMinProfitPercentage)
        CalculateOptimalDiscount(
            decimal basePrice,
            decimal purchasePrice,
            decimal minimumRevenue,
            decimal minimumProfitPercentage,
            decimal bankTax,
            decimal marketplaceTax)
    {
        var totalCommissionRate = (bankTax + marketplaceTax) / 100;
        var netRate = 1 - totalCommissionRate; // dK

        var discountForMinRevenue = CalculateDiscountForMinRevenue(basePrice, purchasePrice, minimumRevenue, netRate);
        var discountForMinProfitPercentage = CalculatediscountForMinProfitPercentage(basePrice, purchasePrice, minimumProfitPercentage, netRate);

        // Choose the smaller discount (more strict condition)
        var finalDiscount = Math.Min(discountForMinRevenue, discountForMinProfitPercentage);
        finalDiscount = Math.Floor(finalDiscount);
        discountForMinRevenue = Math.Floor(discountForMinRevenue);
        discountForMinProfitPercentage = Math.Floor(discountForMinProfitPercentage);

        return (finalDiscount, discountForMinRevenue, discountForMinProfitPercentage);
    }

    private static decimal CalculateDiscountForMinRevenue(decimal basePrice, decimal purchasePrice, decimal minimumRevenue, decimal netRate)
    {
        var xForMinRevenue = (minimumRevenue + purchasePrice) / (basePrice * netRate);
        return Math.Max(0, (1 - xForMinRevenue) * 100);
    }

    private static decimal CalculatediscountForMinProfitPercentage(decimal basePrice, decimal purchasePrice, decimal minimumProfitPercentage, decimal netRate)
    {
        var minProfitPercentageDecimal = minimumProfitPercentage / 100;
        var xForMinProfitPercentage = purchasePrice * (1 + minProfitPercentageDecimal) / (basePrice * netRate);
        var discountForMinProfitPercentage = Math.Max(0, (1 - xForMinProfitPercentage) * 100);
        return discountForMinProfitPercentage;
    }
}
using Microsoft.AspNetCore.OpenApi;
using Microsoft.OpenApi.Models;

namespace Marketplaces.Api.OpenApi;

public sealed class ServerUrlTransformer : IOpenApiDocumentTransformer
{
    public Task TransformAsync(OpenApiDocument document, OpenApiDocumentTransformerContext context, CancellationToken cancellationToken)
    {
        // Workaround to use the Swagger UI "Try Out" functionality when deployed behind a reverse proxy (APIM) with API prefix /sub context configured
        var httpContext = context.ApplicationServices.GetRequiredService<IHttpContextAccessor>().HttpContext;

        if (httpContext != null)
        {
            // Use host and port from current request
            var serverUrl = $"{httpContext.Request.Scheme}://{httpContext.Request.Host}/api";

            document.Servers =
            [
                new() { Url = serverUrl }
            ];
        }

        return Task.CompletedTask;
    }
}

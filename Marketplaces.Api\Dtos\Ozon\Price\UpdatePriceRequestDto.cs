using System.Globalization;
using System.Text.Json.Serialization;

namespace Marketplaces.Api.Dtos.Ozon.Price;

public class UpdatePriceRequestDto(string offerId, decimal price)
{
    [JsonPropertyName("old_price")]
    public string OldPrice { get; set; } = (price * 1.43m).ToString(CultureInfo.InvariantCulture);
    [JsonPropertyName("offer_id")]
    public string OfferId { get; set; } = offerId;
    [JsonPropertyName("min_price")]
    public string MinPrice { get; set; } = (price * 0.9m).ToString(CultureInfo.InvariantCulture);
    public string Price { get; set; } = price.ToString(CultureInfo.InvariantCulture);
    [JsonPropertyName("currency_code")]
    public string CurrencyCode { get; set; } = "RUB";
    [JsonPropertyName("auto_action_enabled")]
    public string AutoActionEnabled { get; set; } = "UNKNOWN";
    [JsonPropertyName("price_strategy_enabled")]
    public string PriceStrategyEnabled { get; set; } = "UNKNOWN"!;
}
﻿// <auto-generated />
using System;
using Marketplaces.Api.Databases;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Marketplaces.Api.Migrations
{
    [DbContext(typeof(DatabaseContext))]
    [Migration("20250822194522_RemoveCurrentPrice")]
    partial class RemoveCurrentPrice
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Marketplaces.Api.Databases.Nomenclature", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Image")
                        .HasColumnType("text");

                    b.Property<decimal?>("MinimumProfitPercentage")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MinimumRevenue")
                        .HasColumnType("numeric");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<decimal?>("PurchasePrice")
                        .HasColumnType("numeric");

                    b.Property<int>("ShopId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("ShopId");

                    b.ToTable("Nomenclature", (string)null);
                });

            modelBuilder.Entity("Marketplaces.Api.Databases.OzonNomenclature", b =>
                {
                    b.Property<Guid>("InternalId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("CommonCode")
                        .HasColumnType("integer");

                    b.Property<decimal?>("EstimatedTax")
                        .HasColumnType("numeric");

                    b.Property<int?>("FBOAmount")
                        .HasColumnType("integer");

                    b.Property<int?>("FBSAmount")
                        .HasColumnType("integer");

                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Image")
                        .HasColumnType("text");

                    b.Property<decimal?>("MarketingPrice")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MarketingSellerPrice")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MinPrice")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MinimumProfitPercentage")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MinimumRevenue")
                        .HasColumnType("numeric");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("NomenclatureId")
                        .HasColumnType("integer");

                    b.Property<decimal?>("OldPrice")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("Price")
                        .HasColumnType("numeric");

                    b.Property<int?>("RedemptionAmount")
                        .HasColumnType("integer");

                    b.Property<decimal?>("RedemptionPercent")
                        .HasColumnType("numeric");

                    b.Property<int?>("ReturnAmount")
                        .HasColumnType("integer");

                    b.Property<string>("SKU")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Size")
                        .HasColumnType("text");

                    b.HasKey("InternalId");

                    b.HasIndex("NomenclatureId")
                        .IsUnique();

                    b.ToTable("OzonNomenclature", (string)null);
                });

            modelBuilder.Entity("Marketplaces.Api.Databases.WildberriesNomenclature", b =>
                {
                    b.Property<Guid>("InternalId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal?>("AcceptanceSum")
                        .HasColumnType("numeric");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal?>("DeductionSum")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("DeliveryPriceSum")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("Discount")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("EstimatedTax")
                        .HasColumnType("numeric");

                    b.Property<int?>("FBSAmount")
                        .HasColumnType("integer");

                    b.Property<int?>("FboAmount")
                        .HasColumnType("integer");

                    b.Property<int>("Id")
                        .HasColumnType("integer");

                    b.Property<string>("Image")
                        .HasColumnType("text");

                    b.Property<decimal?>("MinimumProfitPercentage")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MinimumRevenue")
                        .HasColumnType("numeric");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("NomenclatureId")
                        .HasColumnType("integer");

                    b.Property<decimal?>("PenaltiesSum")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("Price")
                        .HasColumnType("numeric");

                    b.Property<int?>("RedemptionAmount")
                        .HasColumnType("integer");

                    b.Property<decimal?>("RedemptionPercent")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("RetailSum")
                        .HasColumnType("numeric");

                    b.Property<int?>("ReturnAmount")
                        .HasColumnType("integer");

                    b.Property<string>("SKU")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Size")
                        .HasColumnType("text");

                    b.Property<decimal?>("StorageFeeSum")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("ToSendToSellerSum")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("WithoutCommissionSum")
                        .HasColumnType("numeric");

                    b.HasKey("InternalId");

                    b.HasIndex("NomenclatureId")
                        .IsUnique();

                    b.ToTable("WildberriesNomenclature", (string)null);
                });

            modelBuilder.Entity("Marketplaces.Api.Databases.YandexNomenclature", b =>
                {
                    b.Property<Guid>("InternalId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("CommonCode")
                        .HasColumnType("integer");

                    b.Property<int?>("FBSAmount")
                        .HasColumnType("integer");

                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Image")
                        .HasColumnType("text");

                    b.Property<decimal?>("MinimumProfitPercentage")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MinimumRevenue")
                        .HasColumnType("numeric");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("NomenclatureId")
                        .HasColumnType("integer");

                    b.Property<decimal?>("Price")
                        .HasColumnType("numeric");

                    b.Property<string>("SKU")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Size")
                        .HasColumnType("text");

                    b.HasKey("InternalId");

                    b.HasIndex("NomenclatureId")
                        .IsUnique();

                    b.ToTable("YandexNomenclature", (string)null);
                });

            modelBuilder.Entity("Marketplaces.Api.Models.Identity.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsConsentGiven")
                        .HasColumnType("boolean");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MiddleName")
                        .HasColumnType("text");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("PartnerId")
                        .HasColumnType("integer");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("Marketplaces.Api.Models.Identity.ShopUser", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<int>("ShopId")
                        .HasColumnType("integer");

                    b.HasKey("UserId", "ShopId");

                    b.HasIndex("ShopId");

                    b.ToTable("ShopUser", (string)null);
                });

            modelBuilder.Entity("Marketplaces.Api.Models.Partner", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("Fio")
                        .HasColumnType("text");

                    b.Property<string>("Phone")
                        .HasColumnType("text");

                    b.Property<string>("PromoCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("PromoCode")
                        .IsUnique();

                    b.ToTable("Partner", (string)null);
                });

            modelBuilder.Entity("Marketplaces.Api.Models.Payment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset?>("CanceledAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("ConfirmedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ExternalId")
                        .HasColumnType("uuid");

                    b.Property<int>("Price")
                        .HasColumnType("integer");

                    b.Property<int>("ShopId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("Payment", (string)null);
                });

            modelBuilder.Entity("Marketplaces.Api.Models.PriceSnapshot", b =>
                {
                    b.Property<int>("ShopId")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Data")
                        .HasColumnType("jsonb");

                    b.HasKey("ShopId");

                    b.ToTable("PriceSnapshot", (string)null);
                });

            modelBuilder.Entity("Marketplaces.Api.Models.Shop", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("BankTax")
                        .HasColumnType("numeric");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsTrackingEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LastOzonEstimation")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("LastPriceSnapshotTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("LastPriceSyncTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("LastWildberriesEstimation")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("MinimumProfitPercentage")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MinimumRevenue")
                        .HasColumnType("numeric");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("OzonClientId")
                        .HasColumnType("text");

                    b.Property<decimal?>("OzonEstimatedTax")
                        .HasColumnType("numeric");

                    b.Property<string>("OzonKey")
                        .HasColumnType("text");

                    b.Property<int>("OzonMode")
                        .HasColumnType("integer");

                    b.Property<decimal?>("OzonTax")
                        .HasColumnType("numeric");

                    b.Property<string>("WbKey")
                        .HasColumnType("text");

                    b.Property<decimal?>("WildberriesEstimatedTax")
                        .HasColumnType("numeric");

                    b.Property<int>("WildberriesMode")
                        .HasColumnType("integer");

                    b.Property<decimal?>("WildberriesTax")
                        .HasColumnType("numeric");

                    b.Property<int?>("YandexBusinessId")
                        .HasColumnType("integer");

                    b.Property<int?>("YandexCampaignId")
                        .HasColumnType("integer");

                    b.Property<int?>("YandexClientId")
                        .HasColumnType("integer");

                    b.Property<string>("YandexKey")
                        .HasColumnType("text");

                    b.Property<decimal?>("YandexTax")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.ToTable("Shop", (string)null);
                });

            modelBuilder.Entity("Marketplaces.Api.Models.Subscription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset>("EndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ShopId")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("ShopId");

                    b.ToTable("Subscription");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("text");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .HasColumnType("text");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("Marketplaces.Api.Databases.Nomenclature", b =>
                {
                    b.HasOne("Marketplaces.Api.Models.Shop", null)
                        .WithMany()
                        .HasForeignKey("ShopId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Marketplaces.Api.Databases.OzonNomenclature", b =>
                {
                    b.HasOne("Marketplaces.Api.Databases.Nomenclature", null)
                        .WithOne("Ozon")
                        .HasForeignKey("Marketplaces.Api.Databases.OzonNomenclature", "NomenclatureId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Marketplaces.Api.Databases.WildberriesNomenclature", b =>
                {
                    b.HasOne("Marketplaces.Api.Databases.Nomenclature", null)
                        .WithOne("Wildberries")
                        .HasForeignKey("Marketplaces.Api.Databases.WildberriesNomenclature", "NomenclatureId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Marketplaces.Api.Databases.YandexNomenclature", b =>
                {
                    b.HasOne("Marketplaces.Api.Databases.Nomenclature", null)
                        .WithOne("Yandex")
                        .HasForeignKey("Marketplaces.Api.Databases.YandexNomenclature", "NomenclatureId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Marketplaces.Api.Models.Identity.ShopUser", b =>
                {
                    b.HasOne("Marketplaces.Api.Models.Shop", "Shop")
                        .WithMany("ShopUsers")
                        .HasForeignKey("ShopId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Marketplaces.Api.Models.Identity.ApplicationUser", "User")
                        .WithMany("ShopUsers")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Shop");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Marketplaces.Api.Models.PriceSnapshot", b =>
                {
                    b.HasOne("Marketplaces.Api.Models.Shop", null)
                        .WithOne()
                        .HasForeignKey("Marketplaces.Api.Models.PriceSnapshot", "ShopId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Marketplaces.Api.Models.Subscription", b =>
                {
                    b.HasOne("Marketplaces.Api.Models.Shop", null)
                        .WithMany("Subscriptions")
                        .HasForeignKey("ShopId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("Marketplaces.Api.Models.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("Marketplaces.Api.Models.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Marketplaces.Api.Models.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("Marketplaces.Api.Models.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Marketplaces.Api.Databases.Nomenclature", b =>
                {
                    b.Navigation("Ozon");

                    b.Navigation("Wildberries");

                    b.Navigation("Yandex");
                });

            modelBuilder.Entity("Marketplaces.Api.Models.Identity.ApplicationUser", b =>
                {
                    b.Navigation("ShopUsers");
                });

            modelBuilder.Entity("Marketplaces.Api.Models.Shop", b =>
                {
                    b.Navigation("ShopUsers");

                    b.Navigation("Subscriptions");
                });
#pragma warning restore 612, 618
        }
    }
}

using AutoMapper;
using Marketplaces.Api.Authorization;
using Marketplaces.Api.Clients;
using Marketplaces.Api.Databases;
using Marketplaces.Api.Exceptions;
using Marketplaces.Api.Models;
using Marketplaces.Api.Models.Identity;
using Marketplaces.Api.Requests;
using Marketplaces.Api.Responses;
using Marketplaces.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Net.Mime;

namespace Marketplaces.Api.Controllers;

[ApiController]
[Authorize(Policy = Policies.ActiveSubscription)]
[Route("shops/current/stocks")]
[Consumes(MediaTypeNames.Application.Json)]
[Produces(MediaTypeNames.Application.Json)]
public class StocksController(DatabaseContext context,
    IMapper mapper,
    WildberriesClient wbClient,
    OzonClient ozonClient,
    YandexClient yandexClient,
    UserManager<ApplicationUser> userManager, StocksService stocksService) : ShopAbstractController(context)
{
    [HttpPost("sync")]
    public async Task<IActionResult> SyncStocks()
    {
        var user = await userManager.GetUserAsync(User);
        if (user == null)
        {
            return Unauthorized();
        }

        await stocksService.SyncStocks(user.Id);
        return Ok();
    }

    [HttpPut("{nomenclatureId}")]
    public async Task<IActionResult> UpdateStocks(int nomenclatureId, [FromBody] UpdateStocksBody body)
    {
        var shop = await GetShop();
        var nomenclature = await context.FullNomenclatures
            .FirstOrDefaultAsync(n => n.ShopId == shop.Id && n.Id == nomenclatureId);
        if (nomenclature is null)
        {
            throw new NotFoundException();
        }

        if (body.Marketplace is null or Marketplace.Ozon && nomenclature.Ozon != null)
        {
            nomenclature.Ozon.UpdateFbs(body.Amount);
            var warehouses = await ozonClient.GetWarehouseIds(shop);
            await ozonClient.UpdateStocks(shop, warehouses[0], nomenclature.Ozon.Code, body.Amount);
        }

        if (body.Marketplace is null or Marketplace.Wildberries && nomenclature.Wildberries != null)
        {
            var token = shop.GetToken(Marketplace.Wildberries);
            var warehouses = await wbClient.GetWarehouses(token);
            var one = warehouses.FirstOrDefault(w => w.DeliveryType == 1);
            if (one is not null)
            {
                await wbClient.UpdateStocks(token, one.Id, nomenclature.Wildberries.Code,
                    nomenclature.Wildberries.SKU, body.Amount);
                nomenclature.UpdateWildberriesFbs(body.Amount);
            }

        }

        if (body.Marketplace is null or Marketplace.Yandex && nomenclature.Yandex != null)
        {
            nomenclature.UpdateYandexFbs(body.Amount);
            var token = shop.GetToken(Marketplace.Yandex);
            await yandexClient.UpdateStocks(token, nomenclature.Yandex.Code, nomenclature.Yandex.SKU, body.Amount);
        }

        await context.SaveChangesAsync();
        var stocksDto = mapper.Map<StockDto>(nomenclature);
        return Ok(stocksDto);
    }

    [HttpGet]
    public async Task<IActionResult> GetStocks()
    {
        await SyncStocks();
        var user = await userManager.GetUserAsync(User);
        if (user == null)
        {
            return Unauthorized();
        }

        var shop = await context.Shops.Include(s => s.ShopUsers)
            .FirstOrDefaultAsync(s => s.ShopUsers.Select(su => su.UserId).Contains(user.Id));

        if (shop is null)
        {
            throw new NotFoundException();
        }

        var nomenclatures = await context.FullNomenclatures.Where(n => n.ShopId == shop.Id)
            .OrderBy(n => n.Name)
            .ToListAsync();

        nomenclatures.LeaveOnlyWithTokens(shop);
        var stocks = mapper.Map<List<StockDto>>(nomenclatures.SortByWierdRules());
        return Ok(stocks);
    }
}
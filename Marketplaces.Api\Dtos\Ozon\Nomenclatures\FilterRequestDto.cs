using System.Text.Json.Serialization;

namespace Marketplaces.Api.Dtos.Ozon.Nomenclatures;

public class FilterRequestDto
{
    [JsonPropertyName("offer_id")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public List<string>? OfferIds { get; set; }
    [JsonPropertyName("product_id")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public List<string>? ProductIds { get; set; }
}

public class FilterWithLimitRequestDto(FilterRequestDto filter, int limit)
{
    public FilterRequestDto Filter { get; set; } = filter;
    public int Limit { get; set; } = limit;
}
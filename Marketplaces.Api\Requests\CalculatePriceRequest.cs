using Marketplaces.Api.Models;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Marketplaces.Api.Requests;

public class CalculatePriceRequest
{
    [Required(ErrorMessage = "NomenclatureId is required")]
    [Range(1, int.MaxValue, ErrorMessage = "NomenclatureId must be greater than 0")]
    public int NomenclatureId { get; set; }

    [Required(ErrorMessage = "PurchasePrice is required")]
    [Range(0.01, double.MaxValue, ErrorMessage = "PurchasePrice must be greater than 0")]
    public decimal PurchasePrice { get; set; }

    [Required(ErrorMessage = "BasePrice is required")]
    [Range(0.01, double.MaxValue, ErrorMessage = "BasePrice must be greater than 0")]
    public decimal BasePrice { get; set; }

    [Required(ErrorMessage = "DiscountPercent is required")]
    [Range(0, 100, ErrorMessage = "DiscountPercent must be between 0 and 100")]
    public decimal DiscountPercent { get; set; }

    [Required(ErrorMessage = "Marketplace is required")]
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public Marketplace Marketplace { get; set; }
}

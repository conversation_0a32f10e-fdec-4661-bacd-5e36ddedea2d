using Microsoft.AspNetCore.OpenApi;
using Microsoft.OpenApi.Models;

namespace Marketplaces.Api.OpenApi;

public sealed class OpenApiInfoTransformer : IOpenApiDocumentTransformer
{
    public Task TransformAsync(OpenApiDocument document, OpenApiDocumentTransformerContext context, CancellationToken cancellationToken)
    {
        document.Info = new OpenApiInfo
        {
            Title = "MerchantBox API",
            Version = "v1",
            Description = "API for managing marketplaces"
        };
        return Task.CompletedTask;
    }
}

using Marketplaces.Api.Databases;
using Marketplaces.Api.Models;

namespace Marketplaces.Api.Responses;

public class PriceDto
{
    public int Id { get; set; }

    public int? MinAmount { get; set; }

    public string? Name { get; set; }

    public string? Image { get; set; }

    public NestedPriceDto? Ozon { get; set; }

    public NestedPriceDto? Yandex { get; set; }

    public NestedPriceDto? Wildberries { get; set; }

    public decimal? PurchasePrice { get; set; }

    public decimal? MinimumProfitPercentage { get; set; }

    public decimal? MinimumRevenue { get; set; }

    public string? Size { get; set; }

    public void CalculateProfitFields(Nomenclature nomenclature, Shop shop)
    {
        if (Ozon != null && nomenclature.Ozon != null)
        {
            var ozonTax = shop.GetMarketplaceTax(Marketplace.Ozon);
            Ozon.CalculateProfitFields(nomenclature.PurchasePrice, shop.BankTax, ozonTax);
        }

        if (Wildberries != null && nomenclature.Wildberries != null)
        {
            var wbTax = shop.GetMarketplaceTax(Marketplace.Wildberries);
            Wildberries.CalculateProfitFields(nomenclature.PurchasePrice, shop.BankTax, wbTax);
        }

        if (Yandex != null && nomenclature.Yandex != null)
        {
            var yandexTax = shop.GetMarketplaceTax(Marketplace.Yandex);
            Yandex.CalculateProfitFields(nomenclature.PurchasePrice, shop.BankTax, yandexTax);
        }
    }
}
using Marketplaces.Api.Exceptions;
using Marketplaces.Api.Models;

namespace Marketplaces.Api.Databases;

public class Nomenclature
{
    private Nomenclature()
    {
    }

    public Nomenclature(int shopId)
    {
        ShopId = shopId;
        Created = DateTime.UtcNow;
        Updated = DateTime.UtcNow;
    }

    public int Id { get; set; }
    public int ShopId { get; set; }
    public string? Name { get; set; }
    public DateTime Created { get; set; }
    public DateTime Updated { get; set; }

    public string? Image { get; private set; }
    public decimal? MinimumProfitPercentage { get; set; }
    public decimal? MinimumRevenue { get; set; }
    public OzonNomenclature? Ozon { get; private set; }
    public WildberriesNomenclature? Wildberries { get; private set; }
    public YandexNomenclature? Yandex { get; private set; }
    public decimal? PurchasePrice { get; set; }

    public void SetOzonNomenclature(OzonNomenclature nomenclature)
    {
        Name ??= nomenclature.Name;
        Image ??= nomenclature.Image;
        Ozon = nomenclature;
    }

    public void SetWbNomenclature(WildberriesNomenclature nomenclature)
    {
        Name ??= nomenclature.Name;
        Image ??= nomenclature.Image;
        Wildberries = nomenclature;
    }

    public void SetYandexNomenclature(YandexNomenclature nomenclature)
    {
        Name ??= nomenclature.Name;
        Image ??= nomenclature.Image;
        Yandex = nomenclature;
    }

    public string? FindName()
    {
        return Name ?? Wildberries?.Name ?? Ozon?.Name ?? Yandex?.Name;
    }

    public void Merge(Nomenclature nomenclatureToMerge)
    {
        if (Wildberries is null)
        {
            Wildberries = nomenclatureToMerge.Wildberries;
            nomenclatureToMerge.Wildberries = null;
            Wildberries?.SetNomenclatureId(Id);
        }

        if (Ozon is null)
        {
            Ozon = nomenclatureToMerge.Ozon;
            nomenclatureToMerge.Ozon = null;
            Ozon?.SetNomenclatureId(Id);
        }

        if (Yandex is null)
        {
            Yandex = nomenclatureToMerge.Yandex;
            nomenclatureToMerge.Yandex = null;
            Yandex?.SetNomenclatureId(Id);
        }
    }

    public bool HasAnyNomenclatures()
    {
        return Wildberries != null || Ozon != null || Yandex != null;
    }

    public void UpdateWildberriesFbs(int amount)
    {
        if (Wildberries is null)
        {
            throw new ApiException("Wildberries номенклатура не найдена");
        }

        Wildberries.FBSAmount = amount;
    }

    public void UpdateOzoFBOAmount(int? present)
    {
        if (Ozon is null)
        {
            throw new ApiException("Wildberries номенклатура не найдена");
        }

        Ozon.FBOAmount = present;
    }

    public void UpdateYandexFbs(int? count)
    {
        if (Yandex is null)
        {
            throw new ApiException("Wildberries номенклатура не найдена");
        }

        Yandex.FBSAmount = count;
    }

    public int? GetMinFBS()
    {
        var list = new List<int>();
        if (Ozon is { FBSAmount: not null })
        {
            list.Add(Ozon.FBSAmount.Value);
        }

        if (Wildberries is { FBSAmount: not null })
        {
            list.Add(Wildberries.FBSAmount.Value);
        }

        if (Yandex is { FBSAmount: not null })
        {
            list.Add(Yandex.FBSAmount.Value);
        }

        return list.Count == 0 ? null : list.Min();
    }

    public Nomenclature SplitForMarketplace(Marketplace marketplace)
    {
        if (HasOnlyOneNomenclature())
        {
            throw new BadRequestException("Нельзя разделить номенклатуру");
        }

        var nomenclature = new Nomenclature(ShopId);
        switch (marketplace)
        {
            case Marketplace.Ozon:
                if (Ozon == null)
                {
                    throw new BadRequestException("Ozon не установлен");
                }

                nomenclature.SetOzonNomenclature(Ozon);
                nomenclature.UpdateImage(Ozon.Image);
                Ozon = null;
                break;
            case Marketplace.Wildberries:
                if (Wildberries == null)
                {
                    throw new BadRequestException("Wildberries не установлен");
                }

                nomenclature.SetWbNomenclature(Wildberries);
                nomenclature.UpdateImage(Wildberries.Image);
                Wildberries = null;
                break;
            case Marketplace.Yandex:
                if (Yandex == null)
                {
                    throw new BadRequestException("Yandex не установлен");
                }

                nomenclature.SetYandexNomenclature(Yandex);
                nomenclature.UpdateImage(Yandex.Image);
                Yandex = null;
                break;
            default:
                throw new BadRequestException("Указан неверный маркетплейс");
        }

        return nomenclature;
    }

    private bool HasOnlyOneNomenclature()
    {
        var number = 0;
        number += Ozon == null ? 0 : 1;
        number += Yandex == null ? 0 : 1;
        number += Wildberries == null ? 0 : 1;
        return number == 1;
    }

    public bool ContainsCode(string code)
    {
        if (Ozon != null && Ozon.Code == code)
        {
            return true;
        }

        if (Wildberries != null && Wildberries.Code == code)
        {
            return true;
        }

        if (Yandex != null && Yandex.Code == code)
        {
            return true;
        }

        return false;
    }

    public void UpdateImage(string? image, Marketplace? marketplace = null)
    {
        Image ??= image;
        switch (marketplace)
        {
            case Marketplace.Ozon:
                Ozon!.Image = image;
                break;
            case Marketplace.Wildberries:
                Wildberries!.Image = image;
                break;
            case Marketplace.Yandex:
                Yandex!.Image = image;
                break;
            case null:
                break;
        }
    }

    public void UpdatePurchasePrice(decimal? purchasePrice)
    {
        PurchasePrice = purchasePrice;
    }

    public void UpdateMinimums(decimal? minimumProfitPercentage, decimal? minimumRevenue)
    {
        MinimumProfitPercentage = minimumProfitPercentage;
        MinimumRevenue = minimumRevenue;
    }

    public void UpdateYandexPrice(decimal price)
    {
        Yandex?.SetPrice(price);
    }

    public void UpdateWildberriesPrice(decimal price)
    {
        Wildberries?.UpdatePrice(price);
    }

    public void UpdateWildberriesDiscount(decimal? discount)
    {
        Wildberries?.UpdateDiscount(discount);
    }

    public void UpdateWildberriesFbo(int amount)
    {
        Wildberries?.UpdateFbo(amount);
    }

    public MarketplaceNomenclature? GetMarketplaceNomenclature(Marketplace marketplace)
    {
        return marketplace switch
        {
            Marketplace.Wildberries => Wildberries,
            Marketplace.Ozon => Ozon,
            Marketplace.Yandex => Yandex,
            _ => null
        };
    }

    public string? GetSize()
    {
        if (Ozon is { Size: not null })
        {
            return Ozon.Size;
        }

        if (Wildberries is { Size: not null })
        {
            return Wildberries.Size;
        }

        if (Yandex is { Size: not null })
        {
            return Yandex.Size;
        }

        return null;
    }

    public void ResetOzon()
    {
        Ozon = null;
    }

    public void ResetWildberries()
    {
        Wildberries = null;
    }

    public void ResetYandex()
    {
        Yandex = null;
    }

    public void ReduceFbsForAllMarketplacesExcept(Marketplace marketplace, int value)
    {
        if (Wildberries?.FBSAmount != null && marketplace != Marketplace.Wildberries)
        {
            Wildberries.UpdateFbs(Math.Max(0, (int)Wildberries.FBSAmount - value));
        }

        if (Ozon?.FBSAmount != null && marketplace != Marketplace.Ozon)
        {
            Ozon.UpdateFbs(Math.Max(0, (int)Ozon.FBSAmount - value));
        }

        if (Yandex?.FBSAmount != null && marketplace != Marketplace.Yandex)
        {
            Yandex.UpdateFbs(Math.Max(0, (int)Yandex.FBSAmount - value));
        }
    }
}
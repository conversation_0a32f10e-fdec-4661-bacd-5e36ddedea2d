using Marketplaces.Api.Dtos.Yookassa;
using Marketplaces.Api.Exceptions;
using Microsoft.Extensions.Options;
using RestSharp;
using RestSharp.Authenticators;

namespace Marketplaces.Api.Clients;

public class YookassaClient
{
    private const string _currency = "RUB";
    private readonly RestClient _httpClient;
    private readonly AppSettings _appSettings;
    private readonly ILogger<YookassaClient> _logger;

    public YookassaClient(IOptions<AppSettings> options, ILogger<YookassaClient> logger)
    {
        _logger = logger;
        _appSettings = options.Value;
        _httpClient = new RestClient(_appSettings.YookassaBaseUrl,
            clientOptions => clientOptions.Authenticator =
                new HttpBasicAuthenticator(_appSettings.YookassaShopId, _appSettings.YookassaSecretKey));
    }

    public async Task<PaymentDto> CreatePaymentAsync(string userEmail, string userId,
        string idempotenceKey, int price)
    {
        var request = new RestRequest("payments", Method.Post);
        request.AddHeader("Idempotence-Key", idempotenceKey);
        var redirection = new Uri($"{_appSettings.ApplicationUrl}{_appSettings.YookassaReturnUrl}");
        var requestBody = new
        {
            amount = new { value = price, currency = _currency },
            capture = true,
            confirmation = new { type = "redirect", return_url = redirection },
            description = $"Платеж для {userEmail}, userId: {userId}",
            metadata = new { internalId = idempotenceKey },
            merchant_customer_id = userId,
            receipt = new
            {
                customer = new
                {
                    email = userEmail
                },
                items = new[]
                {
                    new
                    {
                        description = "Ежемесячная плата за пользование сервисом",
                        quantity = 1,
                        amount = new { value = price, currency = _currency },
                        vat_code = 1,
                        payment_mode = "full_prepayment",
                        payment_subject = "service"
                    }
                }
            }
        };

        request.AddBody(requestBody);
        var response = await _httpClient.ExecuteAsync<PaymentDto>(request);
        if (!response.IsSuccessful || response.Data == null)
        {
            _logger.LogWarning(response.ErrorException, "Не получилось создать платеж, {Payment}. Content: {Content}",
                idempotenceKey, response.Content);
            throw new BadRequestException("Не получилось создать платеж");
        }

        _logger.LogInformation("Платеж создан, {Payment}", idempotenceKey);
        return response.Data;
    }

    public async Task<PaymentDto?> GetPayment(Guid paymentId)
    {
        var response = await _httpClient.ExecuteAsync<PaymentDto>(new RestRequest($"payments/{paymentId}"));
        if (!response.IsSuccessful || response.Data == null)
        {
            _logger.LogWarning(response.ErrorException, "Не получилось получить платеж, {Payment}", paymentId);
        }

        return response.Data;
    }
}
namespace Marketplaces.Api.Databases;

public class OzonNomenclature : MarketplaceNomenclature
{
    public int? FBSAmount { get; set; }
    public int? FBOAmount { get; set; }

    public OzonNomenclature()
    {
    }

    public OzonNomenclature(long id, string name, string code, string sku, string? image, int? commonCode,
        string? size)
    {
        Id = id;
        Name = name;
        Code = code;
        SKU = sku;
        Image = image;
        CommonCode = commonCode;
        Size = size;
    }

    public Guid InternalId { get; set; }

    public long Id { get; set; }

    public string Name { get; set; } = null!;

    public string Code { get; set; } = null!;

    public string SKU { get; set; }

    public string? Image { get; set; }

    public decimal? Price { get; set; }

    public decimal? MarketingPrice { get; set; }

    public decimal? MarketingSellerPrice { get; set; }

    public decimal? MinPrice { get; set; }

    public decimal? OldPrice { get; set; }

    public decimal? EstimatedTax { get; set; }

    public decimal? RedemptionPercent { get; set; }

    public int? ReturnAmount { get; set; }

    public int? RedemptionAmount { get; set; }

    public int? CommonCode { get; set; }

    // If MarketingSellerPrice is different from Price, it indicates active marketing campaigns
    public bool AnyActiveMarketingCampaigns => MarketingSellerPrice.HasValue
        && Price.HasValue
        && MarketingSellerPrice != Price;

    public void UpdateFbs(int? amount)
    {
        FBSAmount = amount;
    }

    public void UpdateFbo(int? amount)
    {
        FBOAmount = amount;
    }

    public void SetPrice(decimal price, decimal marketingPrice, decimal marketingSellerPrice, decimal minPrice, decimal oldPrice)
    {
        Price = price;
        MarketingPrice = marketingPrice;
        MarketingSellerPrice = marketingSellerPrice;
        MinPrice = minPrice;
        OldPrice = oldPrice;
    }

    public override void UpdateTitle(string name)
    {
        Name = name;
    }

    public void SetReportDetails(decimal? commissionPercent, decimal? redemptionPercent, int redemptionAmount, int returnAmount)
    {
        EstimatedTax = commissionPercent;
        RedemptionPercent = redemptionPercent;
        RedemptionAmount = redemptionAmount;
        ReturnAmount = returnAmount;
    }
}
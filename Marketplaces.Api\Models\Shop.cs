using Marketplaces.Api.Clients;
using Marketplaces.Api.Models.Identity;

namespace Marketplaces.Api.Models;

public class Shop
{
    public Shop()
    {
    }

    public Shop(string name, string userId)
    {
        Name = name;
        var shopUser = new ShopUser()
        {
            UserId = userId,
            ShopId = Id
        };

        ShopUsers.Add(shopUser);
        BankTax = 7;
        YandexTax = 35;
        OzonTax = 35;
        WildberriesTax = 35;
        MinimumProfitPercentage = 30;
        MinimumRevenue = 100;
        WildberriesMode = TaxMode.Manual;
        OzonMode = TaxMode.Manual;
        CreatedAt = DateTimeOffset.UtcNow;
    }

    public int Id { get; set; }
    public string Name { get; set; } = null!;
    public string? OzonClientId { get; set; }
    public int? YandexClientId { get; set; }
    public int? YandexBusinessId { get; set; }
    public int? YandexCampaignId { get; set; }
    public string? OzonKey { get; set; }
    public string? Wb<PERSON>ey { get; set; }
    public string? YandexKey { get; set; }
    public decimal? MinimumProfitPercentage { get; set; }
    public decimal? MinimumRevenue { get; set; }
    public decimal? BankTax { get; set; }
    public decimal? WildberriesTax { get; set; }
    public decimal? WildberriesEstimatedTax { get; set; }
    public TaxMode WildberriesMode { get; set; }
    public decimal? OzonTax { get; set; }
    public decimal? OzonEstimatedTax { get; set; }
    public TaxMode OzonMode { get; set; }
    public decimal? YandexTax { get; set; }
    public DateTimeOffset? LastPriceSyncTime { get; set; }
    public DateTimeOffset? LastPriceSnapshotTime { get; set; }
    public DateTimeOffset? LastWildberriesEstimation { get; set; }
    public DateTimeOffset? LastOzonEstimation { get; set; }
    public DateTimeOffset CreatedAt { get; set; }
    public bool IsTrackingEnabled { get; set; }

    public ICollection<ShopUser> ShopUsers { get; set; } = [];
    public ICollection<Subscription> Subscriptions { get; set; } = [];
    public bool IsSubscriptionEnabled => CreatedAt.AddDays(7) < DateTimeOffset.UtcNow
                                         || Subscriptions.Any(s => s.EndDate < DateTimeOffset.UtcNow);


    public void UpdateToken(string? clientId, string? token, Marketplace marketplace)
    {
        switch (marketplace)
        {
            case Marketplace.Ozon:
                OzonClientId = clientId;
                OzonKey = token;
                break;
            case Marketplace.Wildberries:
                WbKey = token;
                break;
            case Marketplace.Yandex:
                YandexKey = token;
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(marketplace), marketplace, null);
        }
    }


    public bool ContainsToken(Marketplace marketplace)
    {
        return marketplace switch
        {
            Marketplace.Wildberries => WbKey is not null,
            Marketplace.Ozon => OzonClientId is not null && OzonKey is not null,
            Marketplace.Yandex => YandexKey is not null,
            _ => throw new ArgumentOutOfRangeException(nameof(marketplace), marketplace, null)
        };
    }

    public IAuthenticationData? GetToken(Marketplace marketplace)
    {
        return marketplace switch
        {
            Marketplace.Wildberries => WbKey is null ? null : new WbAuthenticationData() { Token = WbKey },
            Marketplace.Ozon => OzonClientId is null
                ? null
                : new OzonAuthenticationData() { ClientId = OzonClientId, ApiKey = OzonKey!, },
            Marketplace.Yandex => YandexKey is null
                ? null
                : new YandexAuthenticationData()
                {
                    ApiKey = YandexKey!,
                    BusinessId = YandexBusinessId!.Value,
                    CampaignId = YandexCampaignId!.Value,
                    ClientId = YandexClientId!.Value,
                },
            _ => throw new ArgumentOutOfRangeException(nameof(marketplace), marketplace, null)
        };
    }

    public void UpdateYandex(int clientId, int businessId, int campaignId)
    {
        YandexClientId = clientId;
        YandexBusinessId = businessId;
        YandexCampaignId = campaignId;
    }

    public void RecordLastPriceSync()
    {
        LastPriceSyncTime = DateTimeOffset.UtcNow;
    }

    public void RecordLastPriceSnapshot()
    {
        LastPriceSnapshotTime = DateTimeOffset.UtcNow;
    }

    public void UpdateInfo(decimal? bankTax, decimal? ozonTax, TaxMode ozonMode, decimal? wbTax,
        TaxMode wildberriesMode, decimal? yandexTax, decimal? minProfit, decimal? minRevenue,
        bool isTrackingEnabled)
    {
        BankTax = bankTax;
        OzonTax = ozonTax;
        OzonMode = ozonMode;
        WildberriesTax = wbTax;
        WildberriesMode = wildberriesMode;
        YandexTax = yandexTax;
        MinimumProfitPercentage = minProfit;
        MinimumRevenue = minRevenue;
        IsTrackingEnabled = isTrackingEnabled;
    }

    public void ResetYandexIds()
    {
        YandexBusinessId = null;
        YandexCampaignId = null;
        YandexClientId = null;
    }

    public void SetWbEstimatedCommission(decimal? wbEstimatedCommission)
    {
        WildberriesEstimatedTax = wbEstimatedCommission;
        LastWildberriesEstimation = DateTimeOffset.UtcNow;
    }

    public void SetOzonEstimatedCommission(decimal? ozonEstimatedCommission)
    {
        OzonEstimatedTax = ozonEstimatedCommission;
        LastOzonEstimation = DateTimeOffset.UtcNow;
    }

    /// <summary>
    /// Gets marketplace commission based on mode (manual or estimated)
    /// </summary>
    /// <param name="marketplace">Marketplace</param>
    /// <returns>Commission to use in calculations</returns>
    public decimal? GetMarketplaceTax(Marketplace marketplace)
    {
        return marketplace switch
        {
            Marketplace.Wildberries => GetMarketplaceTax(WildberriesMode, WildberriesTax, WildberriesEstimatedTax),
            Marketplace.Ozon => GetMarketplaceTax(OzonMode, OzonTax, OzonEstimatedTax),
            Marketplace.Yandex => YandexTax, // For Yandex, there's no estimated commission yet, using only manual
            _ => throw new ArgumentOutOfRangeException(nameof(marketplace), marketplace, null)
        };
    }

    /// <summary>
    /// Gets marketplace commission based on mode (manual or estimated)
    /// </summary>
    /// <param name="mode">Commission mode (Manual or Estimated)</param>
    /// <param name="manualTax">Manual commission</param>
    /// <param name="estimatedTax">Estimated commission</param>
    /// <returns>Commission to use in calculations</returns>
    private static decimal? GetMarketplaceTax(TaxMode mode, decimal? manualTax, decimal? estimatedTax)
    {
        return mode switch
        {
            TaxMode.Manual => manualTax,
            TaxMode.Estimated => estimatedTax ?? manualTax, // If estimated is not available, use manual as fallback
            _ => manualTax
        };
    }

    public Subscription? FindSubscription()
    {
        var now = DateTimeOffset.UtcNow;
        return Subscriptions.FirstOrDefault(s => s.StartDate <= now && s.EndDate >= now);
    }

    public ApplicationUser GetCurrentUser()
    {
        return ShopUsers.First().User;
    }

    public void CrateOrProlongSubscription(DateTimeOffset endDate)
    {
        var subscription = FindSubscription();
        if (subscription is null)
        {
            var newSubscription = new Subscription()
            {
                ShopId = Id,
                StartDate = DateTimeOffset.UtcNow,
                EndDate = endDate
            };
            Subscriptions.Add(newSubscription);
        }
        else
        {
            subscription.EndDate = subscription.EndDate.AddMonths(1);
        }
    }
}
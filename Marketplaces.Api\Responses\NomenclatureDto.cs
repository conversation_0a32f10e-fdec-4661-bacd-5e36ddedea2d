namespace Marketplaces.Api.Responses;

public class NomenclatureDto
{
    public int Id { get; set; }
    public string Name { get; set; } = null!;
    public string? Image { get; set; }
    public OzonDto? Ozon { get; set; }
    public WildberriesDto? Wildberries { get; set; }
    public YandexDto? Yandex { get; set; }
    public decimal? PurchasePrice { get; set; }
    public string? Size { get; set; }

    public class OzonDto
    {
        public long? Id { get; set; }
        public string? Name { get; set; }
        public string? Code { get; set; }
        public string? Sku { get; set; }
        public string? Size { get; set; }
        public decimal? RedemptionPercent { get; set; }
        public decimal? EstimatedTax { get; set; }
    }

    public class WildberriesDto
    {
        public int? Id { get; set; }
        public string? Name { get; set; }
        public string? Code { get; set; }
        public string? Sku { get; set; }
        public string? Size { get; set; }
        public decimal? RedemptionPercent { get; set; }
        public decimal? EstimatedTax { get; set; }
    }

    public class YandexDto
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
        public string? Code { get; set; }
        public string? Sku { get; set; }
        public string? Size { get; set; }
    }
}
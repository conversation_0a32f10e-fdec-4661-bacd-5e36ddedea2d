using Marketplaces.Api.Exceptions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using IExceptionFilter = Microsoft.AspNetCore.Mvc.Filters.IExceptionFilter;

namespace Marketplaces.Api;

internal class ApiExceptionFilter(ILogger<ApiExceptionFilter> logger) : IExceptionFilter
{
    public void OnException(ExceptionContext context)
    {
        switch (context.Exception)
        {
            case BadRequestException bad:
                {
                    context.HttpContext.Response.StatusCode = 400;
                    context.Result = new ObjectResult(
                        new { bad.Message });
                    context.ExceptionHandled = true;
                    break;
                }
            case UnauthorizedException:
                {
                    context.HttpContext.Response.StatusCode = 401;
                    context.ExceptionHandled = true;
                    break;
                }
            case NotFoundException nfe:
                {
                    context.HttpContext.Response.StatusCode = 404;
                    context.ExceptionHandled = true;
                    context.Result = new ObjectResult(
                        new { nfe.Message });
                    break;
                }
            case { } exception:
                logger.LogError(exception, "Server error");
                context.HttpContext.Response.StatusCode = 500;
                context.Result = new ObjectResult(
                    new { Message = exception.Message.ToString() });
                break;
        }
    }
}
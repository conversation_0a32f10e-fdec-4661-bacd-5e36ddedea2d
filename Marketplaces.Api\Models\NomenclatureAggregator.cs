using Marketplaces.Api.Clients;
using Marketplaces.Api.Databases;
using Marketplaces.Api.Dtos.Ozon.Nomenclatures;
using Marketplaces.Api.Dtos.Wildberries;
using Marketplaces.Api.Responses.Wildberries;

namespace Marketplaces.Api.Models;

public static class NomenclatureAggregator
{
    public static void UpsertNewNomenclatures(int shopId, List<Nomenclature> nomenclatures,
        List<WbNomenclatureDto> external)
    {
        foreach (var dto in external)
        {
            foreach (var size in dto.Sizes)
            {

                var current = nomenclatures.Find(n => n.Wildberries?.Code == dto.Code
                                                      && n.Wildberries?.SKU == size.Skus[0]);
                if (current is not null)
                {

                    if (dto.Photos.Count > 0)
                    {
                        current.UpdateImage(dto.Photos[0].c246x328, Marketplace.Wildberries);
                    }

                    var marketplaceNomenclature = current.GetMarketplaceNomenclature(Marketplace.Wildberries);
                    if (marketplaceNomenclature is not null)
                    {
                        marketplaceNomenclature.UpdateSize(size.WbSize);
                        marketplaceNomenclature.UpdateTitle(dto.Title);
                    }
                    continue;
                }

                var wbNomenclature = new WildberriesNomenclature(dto.Id, dto.Title, dto.Code, size.Skus[0], size.WbSize, dto.Photos);
                var baseOne = nomenclatures.Find(n => n.ContainsCode(dto.Code));
                if (baseOne is not null && baseOne.Wildberries is null)
                {
                    baseOne.SetWbNomenclature(wbNomenclature);
                    continue;
                }

                current = new Nomenclature(shopId);
                current.SetWbNomenclature(wbNomenclature);
                nomenclatures.Add(current);
            }
        }
    }

    public static void UpsertNewNomenclatures(int shopId, List<Nomenclature> nomenclatures,
        List<NomenclatureInfoDto> external)
    {
        foreach (var dto in external)
        {
            var current = nomenclatures.Find(n => n.Ozon?.Code == dto.OfferId);
            if (current is not null)
            {
                current.UpdateImage(dto.Images.FirstOrDefault(), Marketplace.Ozon);
                var marketplaceNomenclature = current.GetMarketplaceNomenclature(Marketplace.Ozon);
                if (marketplaceNomenclature is not null)
                {
                    // bad solution
                    current.Ozon!.CommonCode = dto.CommonCode;
                    marketplaceNomenclature.UpdateSize(dto.Size);
                    marketplaceNomenclature.UpdateTitle(dto.Name);
                }

                continue;
            }

            var source = dto.Sources.FirstOrDefault(s => s.Source == "sds")
                         ?? dto.Sources.FirstOrDefault(s => s.Source == "fbs");

            var ozonNomenclature = new OzonNomenclature(dto.Id, dto.Name, dto.OfferId,
                source!.Sku.ToString(), dto.Images.FirstOrDefault(), dto.CommonCode, dto.Size);
            var baseOne = nomenclatures.Find(n => n.ContainsCode(dto.OfferId));
            if (baseOne is not null)
            {
                baseOne.SetOzonNomenclature(ozonNomenclature);
                continue;
            }

            current = new Nomenclature(shopId);
            current.SetOzonNomenclature(ozonNomenclature);
            nomenclatures.Add(current);
        }
    }

    public static void UpsertNewNomenclatures(int shopId, List<Nomenclature> nomenclatures,
        List<YandexNomenclatureDto> external)
    {
        foreach (var dto in external)
        {
            var current = nomenclatures.Find(n => n.Yandex?.Code == dto.Code);
            if (current?.Yandex is not null)
            {
                current.UpdateImage(dto.Image, Marketplace.Yandex);
                current.Yandex.CommonCode = dto.CommonCode;
                continue;
            }

            var yandexNomenclature = new YandexNomenclature(dto.Id, dto.Name, dto.Code, dto.Sku.ToString(), dto.Image, dto.CommonCode);
            var baseOne = nomenclatures.Find(n => n.ContainsCode(dto.Code));
            if (baseOne is not null)
            {
                baseOne.SetYandexNomenclature(yandexNomenclature);
                continue;
            }

            current = new Nomenclature(shopId);
            current.SetYandexNomenclature(yandexNomenclature);
            nomenclatures.Add(current);
        }
    }

    public static void UpsertPrices(List<Nomenclature> yandexNomenclatures, List<YandexPricesDto> yandexPrices)
    {
        foreach (var yandexNomenclature in yandexNomenclatures)
        {
            var price = yandexPrices.Find(p => p.OfferId == yandexNomenclature.Yandex?.Code)?.Price.Value;
            if (price is not null)
            {
                yandexNomenclature.UpdateYandexPrice(price.Value);
            }
        }
    }

    public static void UpsertPrices(List<OzonNomenclature> ozonNomenclatures, List<PriceItemDto> ozonPrices)
    {
        foreach (var ozonNomenclature in ozonNomenclatures)
        {
            var price = ozonPrices.Find(p => p.OfferId == ozonNomenclature.Code)?.Price;
            if (price is not null)
            {
                ozonNomenclature.SetPrice(price.Price,
                    price.MarketingPrice,
                    price.MarketingSellerPrice,
                    price.MinPrice,
                    price.OldPrice);
            }
        }
    }

    public static void UpsertPrices(List<Nomenclature> wbNomenclatures, List<WbPricesDto> wbPrices)
    {
        foreach (var wbNomenclature in wbNomenclatures)
        {
            var price = wbPrices.Find(p => p.Id == wbNomenclature.Wildberries?.Id)?.Sizes[0].Price;
            var discount = wbPrices.Find(p => p.Id == wbNomenclature.Wildberries?.Id)?.Discount;
            if (price is not null)
            {
                wbNomenclature.UpdateWildberriesPrice(price.Value);
                wbNomenclature.UpdateWildberriesDiscount(discount);
            }
        }
    }

    public static decimal? UpsertTaxes(List<Nomenclature> nomenclatures, List<WbReportItemtDto> wbReport)
    {
        var generalCommissionPercent = 0m;
        var generalAmount = 0;
        foreach (var wbNomenclature in nomenclatures
                     .Where(n => n.Wildberries is not null)
                     .Select(n => n.Wildberries!))
        {
            var reportItems = wbReport.Where(r => r.Barcode == wbNomenclature.SKU).ToList();
            var withoutCommissionSum = 0m;
            var retailSum = 0m;
            var returnAmountSum = 0;
            var deliverySum = 0;
            var toSendToSellerSum = 0m;
            var deliveryPriceSum = 0m;
            var deductionSum = 0m;
            var acceptanceSum = 0m;
            var storageFeeSum = 0m;
            var penaltiesSum = 0m;

            foreach (var item in reportItems)
            {
                toSendToSellerSum += item.ToSendToSeller;
                deliveryPriceSum += item.DeliveryPrice;
                deductionSum += item.Deduction;
                acceptanceSum += item.Acceptance;
                storageFeeSum += item.StorageFee;
                penaltiesSum += item.Penalties;


                withoutCommissionSum += item.ToSendToSeller - item.DeliveryPrice - item.Deduction -
                          item.Acceptance - item.StorageFee - item.Penalties;
                retailSum += item.RetailPrice * item.Quantity;
                returnAmountSum += item.ReturnAmount;
                deliverySum += item.DeliveryAmount;
            }

            decimal? commissionPercent = retailSum == 0 ? null : 100 - (withoutCommissionSum * 100 / retailSum);
            decimal? redemptionPercent = (returnAmountSum + deliverySum) == 0 ? null : (decimal)deliverySum / (returnAmountSum + deliverySum) * 100;
            wbNomenclature.SetReportDetails(retailSum, withoutCommissionSum, commissionPercent, redemptionPercent,
                toSendToSellerSum, deliveryPriceSum, deductionSum, acceptanceSum, storageFeeSum, penaltiesSum, deliverySum - returnAmountSum, returnAmountSum);
            if (commissionPercent != null)
            {
                generalCommissionPercent += commissionPercent.Value;
                generalAmount += 1;
            }
        }

        return generalAmount == 0 ? null : generalCommissionPercent / generalAmount;

    }

    public static decimal? UpsertTaxes(List<Nomenclature> nomenclatures, List<Operation> ozonReport)
    {
        var generalCommissionPercent = 0m;
        var generalAmount = 0;
        // var allowedTypes = new List<string> { "orders", "returns", "compensation", "transferDelivery" };
        foreach (var ozonNomenclature in nomenclatures
                     .Where(n => n.Ozon is not null)
                     .Select(n => n.Ozon!))
        {

            var reportItems = ozonReport.Where(r => r.Items.Count != 0
                                                    && r.Items.Any(i => i.Sku.ToString() == ozonNomenclature.SKU)).ToList();
            var goodsCost = 0m;
            var finalSum = 0m;
            var deliveryAmount = 0;
            var returnAmount = 0;
            foreach (var operation in reportItems)
            {
                goodsCost += operation.AccrualsForSale;
                finalSum += operation.Amount;
                switch (operation.Type)
                {
                    case "orders":
                        deliveryAmount += 1;
                        break;
                    case "returns":
                        returnAmount += 1;
                        break;
                }

            }

            decimal? commissionPercent = goodsCost == 0 ? null : 100 - (finalSum * 100 / goodsCost);
            decimal? redemptionPercent = deliveryAmount + returnAmount == 0 ? null : (1 - (decimal)returnAmount / (deliveryAmount + returnAmount)) * 100;
            ozonNomenclature.SetReportDetails(commissionPercent, redemptionPercent, deliveryAmount, returnAmount);
            if (commissionPercent != null)
            {
                generalCommissionPercent += commissionPercent.Value;
                generalAmount += 1;
            }
        }

        return generalAmount == 0 ? null : generalCommissionPercent / generalAmount;
    }
}